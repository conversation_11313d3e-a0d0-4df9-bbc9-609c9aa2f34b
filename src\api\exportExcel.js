import FileSaver from 'file-saver'
import XLSX from 'xlsx'

export function exportXLSX(el, fileName = '表格文件') {
  // 解决生成重复数据-因为使用了fixed属性
  // let fixRight = document.querySelector(el+ " .el-table__fixed-right");
  // let fixLeft = document.querySelector(el+ " .el-table__fixed-left");
  let tableDom = document.querySelector(el).cloneNode(true)
  let tableHeader = tableDom.querySelector('.el-table__header-wrapper')
  let tableBody = tableDom.querySelector('.el-table__body')
  tableHeader.childNodes[0].append(tableBody.childNodes[1])
  let headerDom = tableHeader.childNodes[0].querySelectorAll('th')
  // 移除左侧checkbox的节点
  if (headerDom[0].querySelectorAll('.el-checkbox').length > 0) {
    headerDom[0].remove()
  }
  for (let key in headerDom) {
    if (headerDom[key].innerText === '操作') {
      headerDom[key].remove()
    }
  }
  // 清理掉checkbox 和操作的button
  let tableList = tableHeader.childNodes[0].childNodes[2].querySelectorAll('td')
  for (let key = 0; key < tableList.length; key++) {
    if (
      tableList[key].querySelectorAll('.el-checkbox').length > 0 ||
      tableList[key].querySelectorAll('.el-button').length > 0
    ) {
      tableList[key].remove()
    }
  }
  var wb
  // if (fixRight || fixLeft) {
  //   wb = XLSX.utils.table_to_book(
  //     document.querySelector(el).removeChild(fixRight || fixLeft),
  //     { raw: true }
  //   );
  //   document.querySelector(el).appendChild(fixRight || fixLeft);
  // } else {
  //   wb = XLSX.utils.table_to_book(document.querySelector(el), {
  //     raw: true,
  //   });
  // };

  wb = XLSX.utils.table_to_book(tableHeader)
  // 把当前的book节点写入XLSX中
  let webOut = XLSX.write(wb, {
    bookType: 'xlsx',
    bookSST: true,
    type: 'array',
  })
  try {
    FileSaver.saveAs(
      new Blob([webOut], { type: 'application/octet-stream' }),
      fileName + '.xlsx'
    )
  } catch (e) {
    if (typeof console !== 'undefined') console.log(e, webOut)
  }
}
export function exportRearEnd(el, params) {
  if(params.uncols){
    params.uncols = params.uncols + ","
  }
  if(!params.recols){
    params.recols = {}
  }
  let dateFields = params.dataFields || {}
  let tableDom = document.querySelector(el).cloneNode(true)
  let tableHeader = tableDom.querySelector('.el-table__header-wrapper')
  let headerDom = tableHeader.childNodes[0].querySelectorAll('th')
  let header = []
  let uexportcols = {}

  // 获取表格组件实例来获取正确的列配置
  let tableVueInstance = null
  try {
    // 尝试从DOM元素获取Vue实例
    let tableElement = document.querySelector(el)
    if (tableElement && tableElement.__vue__) {
      tableVueInstance = tableElement.__vue__
    }
  } catch (e) {
    console.warn('无法获取表格Vue实例:', e)
  }

  for (let i = 0; i < headerDom.length; i++) {
    if (headerDom[i].childNodes.length == 0) {
      continue
    }

    // 获取列的字段名
    let cname = ''
    let isOperationColumn = false

    // 首先尝试从表格实例获取列配置
    if (tableVueInstance && tableVueInstance.columns && tableVueInstance.columns[i]) {
      let column = tableVueInstance.columns[i]
      cname = column.property || column.prop || ''
      isOperationColumn = column.type === 'index' || !cname || column.className === 'lesoper'
    } else {
      // 回退到原来的方式
      let classnames = headerDom[i].childNodes[0]['classList']
      cname = classnames[classnames.length - 1]
    }

    // 检查是否为复选框列
    if (headerDom[i].querySelectorAll('.el-checkbox').length > 0) {
      continue
    }

    // 检查是否为需要排除的列
    if (
      isOperationColumn ||
      cname.indexOf('checkbox') != -1 ||
      cname.indexOf('lesoper') != -1 ||
      cname == 'number' ||
      cname === 'index' ||
      !cname
    ) {
      uexportcols[i] = cname
      continue
    }

    let celltype = 'text'
    let cellwidth = '18'
    let dateFormat = 'yyyy-MM-dd HH:mm:ss'
    if (dateFields[cname] && dateFields[cname]['celltype']) {
      celltype = dateFields[cname]['celltype']
    }
    if (dateFields[cname] && dateFields[cname]['dateFormat']) {
      dateFormat = dateFields[cname]['dateFormat']
    }
    if (dateFields[cname] && dateFields[cname]['cellwidth']) {
      cellwidth = dateFields[cname]['cellwidth']
    }
    if(params.uncols && params.uncols.indexOf(cname+",")!=-1){
      continue;
    }
    if(params.recols[cname]){
      cname = params.recols[cname]
    }
    header.push({
      field: cname,
      title: headerDom[i]['outerText'],
      rowspan: headerDom[i]['attributes']['rowspan'].value,
      colspan: headerDom[i]['attributes']['colspan'].value,
      rowspand: headerDom[i]['attributes']['rowspan'].value,
      celltype: celltype,
      cellwidth: cellwidth,
      dateFormat: dateFormat,
      celliswrap: '0',
      cellwraplength: '0',
    })
  }

  let cheaders = []
  cheaders.push(header)

  return Object.assign(
    {
      excelIstmpl: params.excelIstmpl || false,
      excelFileName: params.fileName || '导出数据.xls',
      excelIsnumber: params.isnumber==undefined?true:params.isnumber,
      excelExps: cheaders,
      excelTitle: params.excelTitle || '',
      excelDictionarys: params.dictionarys || {},
      excelTmplListIds: params.excelTmplListIds|| '',
      excelTmplListCcs: params.excelTmplListCcs|| '',
      excelTmplDataCcs: params.excelTmplDataCcs|| '',
      excelTmplDataDds: params.excelTmplDataDds|| '',
      uexportcols:uexportcols
    },
    params.queryForm || { pageNo: 1, pageSize: 20 }
  )
}

export function exportRearEnds(el, params) {
  let dateFields = params.dataFields || {}
  let tableDom = document.querySelector(el).cloneNode(true)
  let tableHeader = tableDom.querySelector('.el-table__header-wrapper')
  let tableHeaderTrs = tableHeader.childNodes[0].querySelectorAll('tr')
  let cheaders = []
  let tablecols = 0
  let tablecolsmap = {}
  let tablecolsheadmap = {}
  let uexportcols = {}
  for (let j = 0; j < tableHeaderTrs.length; j++) {
    let headerDom = tableHeaderTrs[j].querySelectorAll('th')
    let header = []
    for (let i = 0; i < headerDom.length; i++) {
      if (headerDom[i].childNodes.length == 0) {
        continue
      }
      let classnames = headerDom[i].childNodes[0]['classList']
      let cname = classnames[classnames.length - 1]
      if (headerDom[i].querySelectorAll('.el-checkbox').length > 0) {
        continue
      }
      if (
        cname.indexOf('checkbox') != -1 ||
        cname.indexOf('lesoper') != -1 
      ) {
        uexportcols[j] = cname
        continue
      }
      if(params && !params.isserial && cname=='number'){
        continue
      }
      let celltype = 'text'
      let cellwidth = '18'
      if (dateFields[cname] && dateFields[cname]['celltype']) {
        celltype = dateFields[cname]['celltype']
      }
      if (dateFields[cname] && dateFields[cname]['cellwidth']) {
        cellwidth = dateFields[cname]['cellwidth']
      }

      let cn = headerDom[i].classList[0]
      let head = {
        field: cname,
        title: headerDom[i]['outerText'],
        rowspan: headerDom[i]['attributes']['rowspan'].value,
        colspan: headerDom[i]['attributes']['colspan'].value,
        rowspand: headerDom[i]['attributes']['rowspan'].value,
        celltype: celltype,
        cellwidth: cellwidth,
        celliswrap: '0',
        cellwraplength: '0',
        cellid: cn,
        cellpid: cn.substr(0, cn.lastIndexOf('_column_')),
      }
      header.push(head)
      tablecolsmap[cn] = j == 0 ? '0' : cn.substr(0, cn.lastIndexOf('_column_'))
      tablecolsheadmap[cn] = head
      if (j == 0) {
        tablecols =
          tablecols + parseInt(headerDom[i]['attributes']['colspan'].value)
      }
    }
    cheaders.push(header)
  }

  let heads = []
  for (let i = 0; i < tableHeaderTrs.length; i++) {
    let head = []
    for (let j = 0; j < tablecols; j++) {
      head.push({
        field: '',
        title: '',
        rowspan: 1,
        colspan: 1,
        rowspand: 1,
        celltype: 'text',
        cellwidth: 18,
        celliswrap: '0',
        cellwraplength: '0',
      })
    }
    heads.push(head)
  }

  let nheadcolmap = {}
  let rpcol = ''
  for (let i = 0; i < cheaders.length; i++) {
    let rcol = 0
    for (let j = 0; j < cheaders[i].length; j++) {
      let head = cheaders[i][j]
      if (!tablecolsheadmap[head.cellpid]) {
        heads[i][rcol] = head
        nheadcolmap[head.cellid] = rcol
        rcol = rcol + parseInt(head.colspan)
      } else {
        let phead = tablecolsheadmap[head.cellpid]
        if (rpcol != phead.cellid) {
          rpcol = phead.cellid
          rcol = 0
        }

        heads[i][rcol + nheadcolmap[phead.cellid]] = head
        nheadcolmap[head.cellid] = rcol + nheadcolmap[phead.cellid]
        rcol = rcol + parseInt(head.colspan)
      }
    }
    rcol = 0
  }
  return Object.assign(
    {
      excelIstmpl: params.excelIstmpl || false,
      excelFileName: params.fileName || '导出数据.xls',
      excelIsnumber: params.isnumber==undefined?true:params.isnumber,
      excelExps: heads,
      excelTitle: params.excelTitle || '',
      excelDictionarys: params.dictionarys || {},
      excelTmplListIds: params.excelTmplListIds|| '',
      excelTmplListCcs: params.excelTmplListCcs|| '',
      excelTmplDataCcs: params.excelTmplDataCcs|| '',
      excelTmplDataDds: params.excelTmplDataDds|| '',
      uexportcols:uexportcols
    },
    params.queryForm || { pageNo: 1, pageSize: 20 }
  )
}

// 新的导出函数，专门处理Element UI表格
export function exportRearEndImproved(el, params) {
  if(params.uncols){
    params.uncols = params.uncols + ","
  }
  if(!params.recols){
    params.recols = {}
  }

  let dateFields = params.dataFields || {}
  let tableDom = document.querySelector(el)
  let header = []
  let uexportcols = {}

  if (!tableDom) {
    console.error('找不到表格元素:', el)
    return exportRearEnd(el, params) // 回退到原函数
  }

  // 获取表格的所有列配置
  let tableColumns = []
  try {
    // 从DOM中获取所有的el-table-column元素
    let columnElements = tableDom.querySelectorAll('.el-table__header th')

    columnElements.forEach((th, index) => {
      let columnInfo = {
        index: index,
        label: th.textContent.trim(),
        prop: '',
        type: '',
        className: ''
      }

      // 尝试从DOM属性获取列信息
      let cellDiv = th.querySelector('.cell')
      if (cellDiv) {
        let classList = Array.from(cellDiv.classList)

        // 检查是否为特殊列类型
        if (classList.includes('el-table-column--selection')) {
          columnInfo.type = 'selection'
        } else if (th.querySelector('.el-checkbox')) {
          columnInfo.type = 'selection'
        } else if (columnInfo.label === '序号' || classList.some(cls => cls.includes('index'))) {
          columnInfo.type = 'index'
        } else if (columnInfo.label === '操作' || classList.includes('lesoper')) {
          columnInfo.type = 'operation'
          columnInfo.className = 'lesoper'
        } else {
          // 尝试从类名中提取prop - 改进的方法
          let propClass = classList.find(cls =>
            cls.startsWith('el-table_') &&
            cls.includes('__cell')
          )
          if (propClass) {
            // 提取字段名，格式通常是 el-table_1_column_字段名__cell
            let matches = propClass.match(/el-table_\d+_column_(.+?)__cell/)
            if (matches && matches[1]) {
              columnInfo.prop = matches[1]
            }
          }

          // 如果还是没有找到，尝试其他方法
          if (!columnInfo.prop) {
            // 查找包含字段名的类
            let fieldClass = classList.find(cls =>
              !cls.startsWith('el-') &&
              !cls.includes('cell') &&
              !cls.includes('column') &&
              cls.length > 2
            )
            if (fieldClass) {
              columnInfo.prop = fieldClass
            }
          }
        }
      }

      // 如果仍然没有prop，使用label作为fallback
      if (!columnInfo.prop && columnInfo.type !== 'selection' && columnInfo.type !== 'index' && columnInfo.type !== 'operation') {
        columnInfo.prop = columnInfo.label
      }

      tableColumns.push(columnInfo)
    })
  } catch (e) {
    console.warn('解析表格列失败，使用原方法:', e)
    return exportRearEnd(el, params)
  }

  // 构建导出列配置
  tableColumns.forEach((column, i) => {
    // 跳过不需要导出的列
    if (
      column.type === 'selection' ||
      column.type === 'index' ||
      column.type === 'operation' ||
      column.className === 'lesoper' ||
      !column.label ||
      (params.uncols && params.uncols.indexOf(column.prop + ",") !== -1)
    ) {
      uexportcols[i] = column.prop || column.type || 'excluded'
      return
    }

    // 确定字段名
    let fieldName = column.prop || column.label

    // 设置单元格类型和格式
    let celltype = 'text'
    let cellwidth = '18'
    let dateFormat = 'yyyy-MM-dd HH:mm:ss'

    if (dateFields[fieldName] && dateFields[fieldName]['celltype']) {
      celltype = dateFields[fieldName]['celltype']
    }
    if (dateFields[fieldName] && dateFields[fieldName]['dateFormat']) {
      dateFormat = dateFields[fieldName]['dateFormat']
    }
    if (dateFields[fieldName] && dateFields[fieldName]['cellwidth']) {
      cellwidth = dateFields[fieldName]['cellwidth']
    }

    // 处理字段名重命名
    if(params.recols[fieldName]){
      fieldName = params.recols[fieldName]
    }

    header.push({
      field: fieldName,
      title: column.label,
      rowspan: "1",
      colspan: "1",
      rowspand: "1",
      celltype: celltype,
      cellwidth: cellwidth,
      dateFormat: dateFormat,
      celliswrap: '0',
      cellwraplength: '0',
    })
  })

  let heads = [header]

  return Object.assign(
    {
      excelIstmpl: params.excelIstmpl || false,
      excelFileName: params.fileName || '导出数据.xls',
      excelIsnumber: params.isnumber==undefined?true:params.isnumber,
      excelExps: heads,
      excelTitle: params.excelTitle || '',
      excelDictionarys: params.dictionarys || {},
      excelTmplListIds: params.excelTmplListIds|| '',
      excelTmplListCcs: params.excelTmplListCcs|| '',
      excelTmplDataCcs: params.excelTmplDataCcs|| '',
      excelTmplDataDds: params.excelTmplDataDds|| '',
      uexportcols:uexportcols
    },
    params.queryForm || { pageNo: 1, pageSize: 20 }
  )
}

export function exportRearEndDatas(el, params) {
  let dateFields = params.dataFields || {}
  let tableDom = document.querySelector(el).cloneNode(true)
  
  let cheaders = []
  let tablecols = 0
  let tablecolsmap = {}
  let tablecolsheadmap = {}
  let heads = []
  let tableHeader = tableDom.querySelector('.el-table__body-wrapper')
  let tableHeaderTrs = tableHeader.childNodes[0].querySelectorAll('tr')
  for (let j = 0; j < tableHeaderTrs.length; j++) {
    let headerDom = tableHeaderTrs[j].querySelectorAll('td')
    let header = []
    for (let i = 0; i < headerDom.length; i++) {
      if (headerDom[i].childNodes.length == 0) {
        continue
      }
      let classnames = headerDom[i].childNodes[0]['classList']
      let cname = classnames[classnames.length - 1]
      if (headerDom[i].querySelectorAll('.el-checkbox').length > 0) {
        continue
      }
      if (
        cname.indexOf('checkbox') != -1 ||
        cname.indexOf('lesoper') != -1 
      ) {
        continue
      }
      
      if(params && !params.isserial && classnames[0]!='cell'){
        continue
      }

      if(params && params.uncolnums && params.uncolnums.indexOf(i+',')!=-1){
        continue
      }

      if(params && params.uexportcols && params.uexportcols[i]){
        continue
      }

      let celltype = 'text'
      let cellwidth = '18'
      if (dateFields[cname] && dateFields[cname]['celltype']) {
        celltype = dateFields[cname]['celltype']
      }
      if (dateFields[cname] && dateFields[cname]['cellwidth']) {
        cellwidth = dateFields[cname]['cellwidth']
      }

      let cn = headerDom[i].classList[0]
      
      let head = {
        field: cname,
        title: headerDom[i]['outerText'].trim(),
        rowspan: headerDom[i]['attributes']['rowspan'].value,
        colspan: headerDom[i]['attributes']['colspan'].value,
        rowspand: headerDom[i]['attributes']['rowspan'].value,
        celltype: celltype,
        cellwidth: cellwidth,
        celliswrap: '0',
        cellwraplength: '0',
        cellid: cn,
        cellpid: cn.substr(0, cn.lastIndexOf('_column_')),
      }
      header.push(head)
      tablecolsmap[cn] = j == 0 ? '0' : cn.substr(0, cn.lastIndexOf('_column_'))
      tablecolsheadmap[cn] = head
      if (j == 0) {
        tablecols =
          tablecols + parseInt(headerDom[i]['attributes']['colspan'].value)
      }
    }
    cheaders.push(header)
  }

  for (let i = 0; i < tableHeaderTrs.length; i++) {
    let head = []
    
    for (let j = 0; j < tablecols; j++) {
      head.push({
        field: '',
        title: '',
        rowspan: 1,
        colspan: 1,
        rowspand: 1,
        celltype: 'text',
        cellwidth: 18,
        celliswrap: '0',
        cellwraplength: '0',
      })
    }
    heads.push(head)
  }

  let nheadcolmap = {}
  let rpcol = ''
  for (let i = 0; i < cheaders.length; i++) {
    let rcol = 0
    for (let j = 0; j < cheaders[i].length; j++) {
      let head = cheaders[i][j]
      if (!tablecolsheadmap[head.cellpid]) {
        heads[i][rcol] = head
        nheadcolmap[head.cellid] = rcol
        rcol = rcol + parseInt(head.colspan)
      } else {
        let phead = tablecolsheadmap[head.cellpid]
        if (rpcol != phead.cellid) {
          rpcol = phead.cellid
          rcol = 0
        }

        heads[i][rcol + nheadcolmap[phead.cellid]] = head
        nheadcolmap[head.cellid] = rcol + nheadcolmap[phead.cellid]
        rcol = rcol + parseInt(head.colspan)
      }
    }
    rcol = 0
  }
  return Object.assign(
    {
      excelIstmpl: params.excelIstmpl || false,
      excelFileName: params.fileName || '导出数据.xls',
      excelIsnumber: params.isnumber==undefined?true:params.isnumber,
      excelExps: heads,
      excelTitle: params.excelTitle || ''
    },
    {}
  )
}

export function exportGetPageData(qf,qd){
  let ja = qf.excelExps[qf.excelExps.length-1]
  let ed = []
  for(let i=0;i<qd.excelExps.length;i++){
    let rd = {}
    for(let j=0;j<qd.excelExps[i].length;j++){
      rd[ja[j].field] = qd.excelExps[i][j].title
    }
    ed.push(rd)
  }
  return ed
}



export function exportRearTmplVxe(params) {
  if(params.uncols){
    params.uncols = params.uncols + ","
  }
  let dateFields = params.dataFields || {}
  let header = []
  for (let i = 0; i < params.columns.length; i++) {
    let celltype = 'text'
    let cellwidth = '18'
    let dateFormat = 'yyyy-MM-dd HH:mm:ss'
    let cname = params.columns[i].prop
    if (dateFields[cname] && dateFields[cname]['celltype']) {
      celltype = dateFields[cname]['celltype']
    }
    if (dateFields[cname] && dateFields[cname]['dateFormat']) {
      dateFormat = dateFields[cname]['dateFormat']
    }
    if (dateFields[cname] && dateFields[cname]['cellwidth']) {
      cellwidth = dateFields[cname]['cellwidth']
    }
    if(params.uncols && params.uncols.indexOf(cname+",")!=-1){
      continue;
    }
    header.push({
      field: cname,
      title: params.columns[i].label,
      rowspan: 1,
      colspan: 1,
      rowspand: 1,
      celltype: celltype,
      cellwidth: cellwidth,
      dateFormat: dateFormat,
      celliswrap: '0',
      cellwraplength: '0',
    })
  }

  let cheaders = []
  cheaders.push(header)

  return Object.assign(
    {
      excelIstmpl: params.excelIstmpl || false,
      excelFileName: params.fileName || '导出数据.xls',
      excelIsnumber: params.isnumber==undefined?true:params.isnumber,
      excelExps: cheaders,
      excelTitle: params.excelTitle || '',
      excelDictionarys: params.dictionarys || {},
      excelTmplListIds: params.excelTmplListIds|| '',
      excelTmplListCcs: params.excelTmplListCcs|| '',
      excelTmplDataCcs: params.excelTmplDataCcs|| '',
      excelTmplDataDds: params.excelTmplDataDds|| ''
    },
    params.queryForm || { pageNo: 1, pageSize: 20 }
  )
}
