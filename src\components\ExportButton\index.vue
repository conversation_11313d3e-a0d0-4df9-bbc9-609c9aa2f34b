<!--导出按钮组件-->
<template>
  <div class="export-button-group">
    <!-- 单个导出按钮 -->
    <el-button
      v-if="!showDropdown"
      :type="buttonType"
      :size="buttonSize"
      :icon="buttonIcon"
      :loading="exporting"
      @click="handleExport"
      :disabled="disabled"
    >
      {{ buttonText }}
    </el-button>

    <!-- 下拉菜单导出按钮 -->
    <el-dropdown v-else @command="handleDropdownCommand" :disabled="disabled">
      <el-button
        :type="buttonType"
        :size="buttonSize"
        :loading="exporting"
      >
        {{ buttonText }}<i class="el-icon-arrow-down el-icon--right"></i>
      </el-button>
      <el-dropdown-menu slot="dropdown">
        <el-dropdown-item command="page" icon="el-icon-download">导出当前数据</el-dropdown-item>
        <el-dropdown-item command="all" icon="el-icon-download">导出全部数据</el-dropdown-item>
      </el-dropdown-menu>
    </el-dropdown>
  </div>
</template>

<script>
import { exportRearEndImproved } from '@/api/exportExcel'
import { baseURL } from '@/config'

export default {
  name: 'ExportButton',
  props: {
    // 导出API函数
    exportApi: {
      type: Function,
      required: true
    },
    // 表格选择器
    tableSelector: {
      type: String,
      required: true
    },
    // 表格列配置（可选，用于精确控制导出字段）
    tableColumns: {
      type: Array,
      default: () => []
    },
    // 查询表单数据
    queryForm: {
      type: Object,
      default: () => ({})
    },
    // 导出文件名
    fileName: {
      type: String,
      default: '导出数据.xls'
    },
    // Excel标题
    excelTitle: {
      type: String,
      default: '导出数据'
    },
    // 日期字段配置
    dateFields: {
      type: Object,
      default: () => ({})
    },
    // 按钮类型
    buttonType: {
      type: String,
      default: 'success'
    },
    // 按钮大小
    buttonSize: {
      type: String,
      default: 'small'
    },
    // 按钮图标
    buttonIcon: {
      type: String,
      default: 'el-icon-download'
    },
    // 按钮文字
    buttonText: {
      type: String,
      default: '导出'
    },
    // 是否禁用
    disabled: {
      type: Boolean,
      default: false
    },
    // 是否显示序号
    showNumber: {
      type: Boolean,
      default: true
    },
    // 额外的导出参数
    extraParams: {
      type: Object,
      default: () => ({})
    },
    // 是否显示下拉菜单
    showDropdown: {
      type: Boolean,
      default: false
    },
    // 全部数据导出时的页面大小
    allDataPageSize: {
      type: Number,
      default: 10000
    },
    // 排除的列名（不导出的列）
    excludeColumns: {
      type: Array,
      default: () => []
    },
    // 自动排除操作列
    autoExcludeOperations: {
      type: Boolean,
      default: true
    }
  },
  data() {
    return {
      exporting: false
    }
  },
  methods: {
    // 构建导出数据配置
    buildExportData(params) {
      if(params.uncols){
        params.uncols = params.uncols + ","
      }
      if(!params.recols){
        params.recols = {}
      }

      let dateFields = params.dataFields || {}
      let header = []
      let uexportcols = {}

      // 如果传入了列配置，使用传入的配置
      if (this.tableColumns && this.tableColumns.length > 0) {
        this.tableColumns.forEach((column, index) => {
          // 跳过不需要导出的列
          if (
            column.type === 'selection' ||
            column.type === 'index' ||
            !column.prop ||
            column.label === '操作' ||
            (column.className && column.className.includes('lesoper')) ||
            (params.uncols && params.uncols.indexOf(column.prop + ",") !== -1)
          ) {
            uexportcols[index] = column.prop || column.type || 'excluded'
            return
          }

          // 确定字段名
          let fieldName = column.prop

          // 设置单元格类型和格式
          let celltype = 'text'
          let cellwidth = '18'
          let dateFormat = 'yyyy-MM-dd HH:mm:ss'

          if (dateFields[fieldName] && dateFields[fieldName]['celltype']) {
            celltype = dateFields[fieldName]['celltype']
          }
          if (dateFields[fieldName] && dateFields[fieldName]['dateFormat']) {
            dateFormat = dateFields[fieldName]['dateFormat']
          }
          if (dateFields[fieldName] && dateFields[fieldName]['cellwidth']) {
            cellwidth = dateFields[fieldName]['cellwidth']
          }

          // 处理字段名重命名
          if(params.recols[fieldName]){
            fieldName = params.recols[fieldName]
          }

          header.push({
            field: fieldName,
            title: column.label,
            rowspan: "1",
            colspan: "1",
            rowspand: "1",
            celltype: celltype,
            cellwidth: cellwidth,
            dateFormat: dateFormat,
            celliswrap: '0',
            cellwraplength: '0',
          })
        })
      } else {
        // 回退到原来的方法
        return exportRearEndImproved(this.tableSelector, params)
      }

      let heads = [header]

      return Object.assign(
        {
          excelIstmpl: params.excelIstmpl || false,
          excelFileName: params.fileName || '导出数据.xls',
          excelIsnumber: params.isnumber==undefined?true:params.isnumber,
          excelExps: heads,
          excelTitle: params.excelTitle || '',
          excelDictionarys: params.dictionarys || {},
          excelTmplListIds: params.excelTmplListIds|| '',
          excelTmplListCcs: params.excelTmplListCcs|| '',
          excelTmplDataCcs: params.excelTmplDataCcs|| '',
          excelTmplDataDds: params.excelTmplDataDds|| '',
          uexportcols:uexportcols
        },
        params.queryForm || { pageNo: 1, pageSize: 20 }
      )
    },

    // 下拉菜单命令处理
    handleDropdownCommand(command) {
      if (command === 'page') {
        this.handleExportPage()
      } else if (command === 'all') {
        this.handleExportAll()
      }
    },

    // 导出数据（兼容单按钮模式）
    async handleExport() {
      if (this.showDropdown) {
        // 如果是下拉模式，默认导出当前数据
        this.handleExportPage()
      } else {
        // 单按钮模式，导出当前数据
        this.handleExportPage()
      }
    },

    // 导出当前页数据
    async handleExportPage() {
      if (this.exporting) return

      this.exporting = true
      try {
        // 构建排除列字符串
        let excludeCols = this.buildExcludeColumns()

        // 构建导出参数
        let params = {
          dataFields: this.dateFields,
          fileName: this.fileName,
          isnumber: this.showNumber,
          excelTitle: this.excelTitle,
          queryForm: this.queryForm,
          uncols: excludeCols, // 添加排除列参数
          ...this.extraParams
        }

        // 使用改进版的导出函数处理表格数据
        let exportData = this.buildExportData(params)

        // 调用导出API
        const response = await this.exportApi(exportData)

        if (response && response.msg) {
          // 打开下载链接
          window.open(baseURL + "/" + response.msg)
          this.$message.success('当前数据导出成功')
          this.$emit('export-success', response)
        } else {
          throw new Error('导出响应格式错误')
        }
      } catch (error) {
        console.error('导出失败:', error)
        this.$message.error(error.message || '导出失败，请重试')
        this.$emit('export-error', error)
      } finally {
        this.exporting = false
      }
    },

    // 导出全部数据
    async handleExportAll() {
      if (this.exporting) return

      this.exporting = true
      try {
        // 构建排除列字符串
        let excludeCols = this.buildExcludeColumns()

        // 构建全部数据导出参数
        let allDataQueryForm = {
          ...this.queryForm,
          pageNo: 1,
          pageSize: this.allDataPageSize
        }

        let params = {
          dataFields: this.dateFields,
          fileName: this.fileName.replace('.xls', '_全部数据.xls'),
          isnumber: this.showNumber,
          excelTitle: this.excelTitle + '(全部数据)',
          queryForm: allDataQueryForm,
          uncols: excludeCols, // 添加排除列参数
          ...this.extraParams
        }

        // 使用改进版的导出函数处理表格数据
        let exportData = this.buildExportData(params)

        // 调用导出API
        const response = await this.exportApi(exportData)

        if (response && response.msg) {
          // 打开下载链接
          window.open(baseURL + "/" + response.msg)
          this.$message.success('全部数据导出成功')
          this.$emit('export-all-success', response)
        } else {
          throw new Error('全部数据导出响应格式错误')
        }
      } catch (error) {
        console.error('全部数据导出失败:', error)
        this.$message.error(error.message || '全部数据导出失败，请重试')
        this.$emit('export-all-error', error)
      } finally {
        this.exporting = false
      }
    },

    // 构建排除列字符串
    buildExcludeColumns() {
      let excludeCols = [...this.excludeColumns]

      // 自动排除操作列和其他不需要的列
      if (this.autoExcludeOperations) {
        excludeCols.push(
          'oper',        // 操作列
          'operation',   // 操作列
          'action',      // 操作列
          'operations',  // 操作列
          'lesoper',     // LES操作列
          'checkbox',    // 复选框列
          'selection'    // 选择列
        )
      }

      // 返回逗号分隔的字符串，末尾加逗号
      return excludeCols.length > 0 ? excludeCols.join(',') + ',' : ''
    }
  }
}
</script>

<style scoped>
.export-button-group {
  display: inline-block;
}

.export-button-group .el-dropdown {
  vertical-align: top;
}

.export-button-group .el-button {
  margin-right: 0;
}

/* 下拉菜单项图标样式 */
.export-button-group .el-dropdown-menu__item {
  display: flex;
  align-items: center;
}

.export-button-group .el-dropdown-menu__item i {
  margin-right: 8px;
  width: 14px;
}
</style>
