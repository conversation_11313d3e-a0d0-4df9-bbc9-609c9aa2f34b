<!--实物资产档案 - 土地-->
<template>
  <div class="land-container">
    <!-- 顶部统计卡片 -->
    <div class="statistics-cards">
      <div class="stat-card">
        <div class="icon-wrapper red-bg">
          <i class="el-icon-map-location" />
        </div>
        <div class="stat-info">
          <div class="stat-title">总土地面积</div>
          <div class="stat-value red-text">{{ statistics.totalCount || 0 }}<span>宗</span></div>
        </div>
      </div>

      <div class="stat-card">
        <div class="icon-wrapper orange-bg">
          <i class="el-icon-money" />
        </div>
        <div class="stat-info">
          <div class="stat-title">土地总价值</div>
          <div class="stat-value orange-text">{{ statistics.totalValue || 0 }}<span>亿元</span></div>
        </div>
      </div>

      <div class="stat-card">
        <div class="icon-wrapper blue-bg">
          <i class="el-icon-data-line" />
        </div>
        <div class="stat-info">
          <div class="stat-title">本年度处置项目数</div>
          <div class="stat-value blue-text">{{ statistics.disposalCount || 0 }}<span>项</span></div>
        </div>
      </div>

      <div class="stat-card">
        <div class="icon-wrapper purple-bg">
          <i class="el-icon-key" />
        </div>
        <div class="stat-info">
          <div class="stat-title">本年度租赁项目数</div>
          <div class="stat-value purple-text">{{ statistics.leaseCount || 0 }}<span>项</span></div>
        </div>
      </div>
    </div>

    <!-- 搜索表单 -->
    <div class="search-form">
      <el-form :inline="true" :model="searchForm" class="form-inline">
        <div class="leftRight">
          <div class="leftBar">
            <el-form-item label="资产编号:">
              <el-input v-model="searchForm.zcliAssetsNo" placeholder="请输入资产编号" class="inputW" />
            </el-form-item>
            <el-form-item label="土地权属证号:">
              <el-input v-model="searchForm.zcliCertificateCode" placeholder="请输入土地权属证明编号" class="inputW" />
            </el-form-item>
            <el-form-item label="现状/用途:">
              <el-input v-model="searchForm.zcliUseDescribe" placeholder="请输入现状/用途" class="inputW" />
            </el-form-item>
          </div>
          <el-form-item>
            <el-button type="primary" @click="onSearch">查询</el-button>
            <el-button type="primary" @click="onSearch" icon="el-icon-search">高级查询</el-button>
            <el-button @click="resetQuery">重置</el-button>
            <el-button @click="onExport">导出</el-button>
          </el-form-item>
        </div>
      </el-form>
    </div>

    <!-- 数据表格 -->
    <div class="table-section">
      <el-table id="landTable" :data="tableData" :height="470" border stripe highlight-current-row style="width: 100%;" v-loading="loading" row-key="zcliId">
        <el-table-column type="index" label="序号" width="60" align="center" />
        <el-table-column prop="zcliAssetsNo" label="资产编号" width="100" show-overflow-tooltip align="center" />
        <el-table-column prop="zcliUseDescribe" label="现状/用途" width="120" show-overflow-tooltip align="center" />
        <el-table-column prop="companyName" label="产权单位" width="120" show-overflow-tooltip align="center" />
        <el-table-column prop="zcliCertificateCode" label="土地权属证号" width="120" show-overflow-tooltip align="center" />
        <el-table-column prop="zcliAddress" label="具体土地位置" width="140" show-overflow-tooltip align="center" />
        <el-table-column prop="zcliType" label="取得方式" width="100" show-overflow-tooltip align="center" />
        <el-table-column prop="zcliDate" label="登记时间" width="120" show-overflow-tooltip align="center" />
        <el-table-column prop="zcliArea" label="土地总面积(㎡)" width="120" align="center" />
        <el-table-column prop="zcliBookValue" label="原值(万元)" width="120" show-overflow-tooltip align="center" />
        <el-table-column prop="zcliNetbookValue" label="净值(万元)" width="120" show-overflow-tooltip align="center" />
        <el-table-column prop="zcliTotalDepreciation" label="本年计提折旧(万元)" width="150" show-overflow-tooltip align="center" />
        <el-table-column prop="zcliRange" label="境内/境外" width="100" show-overflow-tooltip align="center" />
        <el-table-column prop="zcliIfAssets" label="是否两非资产" width="120" align="center" />
        <el-table-column prop="zcliIfExist" label="是否取得土地权属证明" width="170" show-overflow-tooltip align="center" />
        <el-table-column prop="zcliIfDispute" label="是否存在纠纷" width="120" show-overflow-tooltip align="center" />
        <el-table-column prop="zcliIfDispose" label="是否可处置" width="120" show-overflow-tooltip align="center" />
        <el-table-column prop="zcliIfMortgage" label="是否已抵押" width="120" show-overflow-tooltip align="center" />
        <el-table-column prop="zcliOperator" label="经办人" width="100" show-overflow-tooltip align="center" />
        <el-table-column prop="zcliOperatorTel" label="经办人联系方式" width="120" show-overflow-tooltip align="center" />
        <el-table-column prop="zcliProvince" label="省份" width="100" show-overflow-tooltip align="center" />
        <el-table-column prop="zcliCity" label="城市" width="100" show-overflow-tooltip align="center" />
        <el-table-column prop="zcliCounty" label="区/县" width="100" show-overflow-tooltip align="center" />
        <el-table-column prop="zcliDeptName" label="业务主管部门" width="140" show-overflow-tooltip align="center" />
        <el-table-column prop="zcliDepartmentLeader" label="部门负责人" width="120" show-overflow-tooltip align="center" />
        <el-table-column prop="zcliDepartmentTel" label="部门负责人联系方式" width="150" show-overflow-tooltip align="center" />
        <el-table-column prop="zcliCompanyLeader" label="分管所(公司)领导" width="140" show-overflow-tooltip align="center" />
        <el-table-column prop="zcliCompanyTel" label="分管所(公司)领导联系方式" width="110" show-overflow-tooltip align="center" />
        <el-table-column prop="zcliEvaluateValue" label="最近评估价值(万元)" width="150" show-overflow-tooltip align="center" />
        <el-table-column prop="zcliEvaluateDate" label="最近评估日期" width="120" show-overflow-tooltip align="center" />
        <el-table-column prop="zcliServiceLife" label="土地使用年限(年)" width="140" show-overflow-tooltip align="center" />
        <el-table-column prop="zcliDepreciableYear" label="折旧年限" width="100" show-overflow-tooltip align="center" />
        <el-table-column prop="zcliRemark" label="备注" width="150" show-overflow-tooltip align="center" />
        <el-table-column prop="createdTime" label="创建时间" width="120" show-overflow-tooltip align="center" />
        <el-table-column prop="createdBy" label="创建人" width="100" show-overflow-tooltip align="center" />
        <el-table-column prop="updatedTime" label="更新时间" width="120" show-overflow-tooltip align="center" />
        <el-table-column label="操作" width="80" fixed="right" align="center">
          <template slot-scope="scope">
            <el-button type="text" size="small" @click="handleDetail(scope.row)">详情</el-button>
          </template>
        </el-table-column>
      </el-table>

      <!-- 分页 -->
      <el-pagination background class="el-pagination-a" @current-change="handleCurrentChange" @size-change="handleSizeChange" :current-page="currentPage" :page-size="pageSize" :page-sizes="[10, 20, 50, 100]" layout="total, sizes, prev, pager, next, jumper" :total="total" />
    </div>

    <!-- 详情弹窗组件 -->
    <land-detail-dialog ref="landDetail" />
  </div>
</template>

<script>
import { getLandsList, exportLands, getLandsStats } from '@/api/land'
import LandDetailDialog from './components/LandDetailDialog.vue'

export default {
  name: "LandIndex",
  components: {
    LandDetailDialog
  },
  data () {
    return {
      searchForm: {
        zcliAssetsNo: '',
        zcliCertificateCode: '',
        companyName: '',
        zcliOperator: '',
        zcliCity: '',
        zcliAddress: '',
        zcliUseDescribe: '',
        zcliIfDispose: '',
        zcliIfDispute: '',
        zcliIfMortgage: ''
      },
      tableData: [],
      currentPage: 1,
      pageSize: 10,
      total: 0,
      loading: false,
      statistics: {
        totalCount: 0,
        totalValue: 0,
        totalArea: 0,
        disposalCount: 0,
        leaseCount: 0
      },
    }
  },
  created () {
    this.fetchData()
    this.fetchStatistics()
  },
  methods: {
    fetchData () {
      this.loading = true
      const query = {
        ...this.searchForm,
        pageNo: this.currentPage,
        pageSize: this.pageSize
      }

      getLandsList(query).then(response => {
        if (response && response.data) {
          this.tableData = response.data.list || []
          this.total = response.data.total || 0
        } else {
          this.tableData = []
          this.total = 0
        }
        this.loading = false
      }).catch(() => {
        this.loading = false
        this.$message.error('获取数据失败')
      })
    },

    fetchStatistics () {
      getLandsStats().then(response => {
        if (response && response.data) {
          this.statistics = {
            totalCount: response.data.TOTAL_LAND || 0,
            totalValue: response.data.TOTAL_VALUE || 0,
            totalArea: response.data.totalArea || 0,
            disposalCount: response.data.disposalCount || 0,
            leaseCount: response.data.leaseCount || 0
          }
        }
      }).catch(() => {
        this.$message.error('获取统计数据失败')
        // 使用 Mock 统计数据
        this.statistics = {
          totalCount: 156,
          totalValue: 23.5,
          totalArea: 1250000,
          disposalCount: 8,
          leaseCount: 12
        }
      })
    },

    onSearch () {
      this.currentPage = 1
      this.fetchData()
    },

    resetQuery () {
      this.currentPage = 1
      this.searchForm = {
        zcliAssetsNo: '',
        zcliCertificateCode: '',
        companyName: '',
        zcliOperator: '',
        zcliCity: '',
        zcliAddress: '',
        zcliUseDescribe: '',
        zcliIfDispose: '',
        zcliIfDispute: '',
        zcliIfMortgage: ''
      }
      this.fetchData()
    },

    onExport () {
      const query = {
        ...this.searchForm
      }
      exportLands(query).then(() => {
        this.$message.success('导出成功')
      }).catch(() => {
        this.$message.error('导出失败')
      })
    },

    handleDetail (row) {
      this.$refs.landDetail.showDialog(row)
    },

    handleSizeChange (val) {
      this.pageSize = val
      this.fetchData()
    },

    handleCurrentChange (val) {
      this.currentPage = val
      this.fetchData()
    }
  }
}
</script>

<style lang="scss" scoped>
.leftRight {
  display: flex;
  justify-content: space-between;
}
.inputW {
  width: 250px;
}

.land-container {
  padding: 16px;
  background-color: #f5f7fa;
}

/* 顶部统计卡片样式 */
.statistics-cards {
  display: flex;
  justify-content: space-between;
  margin-bottom: 20px;
}

.stat-card {
  flex: 1;
  display: flex;
  align-items: center;
  background-color: #fff;
  padding: 20px;
  border-radius: 4px;
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.05);
  margin-right: 15px;
}

.stat-card:last-child {
  margin-right: 0;
}

.icon-wrapper {
  width: 48px;
  height: 48px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 15px;
}

.icon-wrapper i {
  font-size: 24px;
  color: #fff;
}

.red-bg {
  background-color: rgba(245, 108, 108, 0.2);
}

.orange-bg {
  background-color: rgba(230, 162, 60, 0.2);
}

.blue-bg {
  background-color: rgba(64, 158, 255, 0.2);
}

.purple-bg {
  background-color: rgba(103, 194, 58, 0.2);
}

.stat-info {
  flex: 1;
}

.stat-title {
  font-size: 14px;
  color: #606266;
  margin-bottom: 8px;
}

.stat-value {
  font-size: 20px;
  font-weight: bold;
}

.stat-value span {
  font-size: 14px;
  margin-left: 4px;
}

.red-text {
  color: #f56c6c;
}

.orange-text {
  color: #e6a23c;
}

.blue-text {
  color: #409eff;
}

.purple-text {
  color: #67c23a;
}

/* 搜索表单样式 */
.search-form {
  background-color: #fff;
  padding: 20px 20px 0 20px;
  border-radius: 4px;
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.05);
  margin-bottom: 20px;
}

/* 数据表格样式 */
.table-section {
  background: white;
  border-radius: 4px;
}
</style>
