<!--实物资产档案-->
<template>
  <div class="custom-table-container">
    <!-- 顶部统计卡片 -->
    <div class="statistics-cards">
      <div class="stat-card">
        <div class="icon-wrapper red-bg">
          <i class="el-icon-office-building" />
        </div>
        <div class="stat-info">
          <div class="stat-title">总房屋数量</div>
          <div class="stat-value red-text">{{ statistics.totalCount || 0 }}<span>间</span></div>
        </div>
      </div>

      <div class="stat-card">
        <div class="icon-wrapper orange-bg">
          <i class="el-icon-money" />
        </div>
        <div class="stat-info">
          <div class="stat-title">房屋总价值</div>
          <div class="stat-value orange-text">{{ statistics.totalValue || 0 }}<span>亿元</span></div>
        </div>
      </div>

      <div class="stat-card">
        <div class="icon-wrapper blue-bg">
          <i class="el-icon-data-line" />
        </div>
        <div class="stat-info">
          <div class="stat-title">本年度处置项目数</div>
          <div class="stat-value blue-text">{{ statistics.disposalCount || 0 }}<span>项</span></div>
        </div>
      </div>

      <div class="stat-card">
        <div class="icon-wrapper purple-bg">
          <i class="el-icon-key" />
        </div>
        <div class="stat-info">
          <div class="stat-title">本年度租赁项目数</div>
          <div class="stat-value purple-text">{{ statistics.leaseCount || 0 }}<span>项</span></div>
        </div>
      </div>
    </div>

    <!-- 搜索表单 -->
    <div class="search-form">
      <el-form :inline="true" :model="searchForm" class="form-inline">
        <div class="leftRight">
          <div class="leftBar">
            <el-form-item label="资产名称:">
              <el-input v-model="searchForm.zchiAssetsNo" placeholder="请输入房屋名称" class="inputW" />
            </el-form-item>
            <el-form-item label="产权证号:">
              <el-input v-model="searchForm.zchiCertificateCode" placeholder="请输入产权证号" class="inputW" />
            </el-form-item>
            <el-form-item label="现状/用途:">
              <el-input v-model="searchForm.zchiUseDescribe" placeholder="请输入现状/用途" class="inputW" />
            </el-form-item>
          </div>
          <el-form-item>
            <el-button type="primary" @click="onSearch">查询</el-button>
            <el-button type="primary" @click="handleAdvancedSearch" icon="el-icon-search">高级查询</el-button>
            <el-button @click="resetQuery" style="margin-right:10px">重置</el-button>
            <ExportButton :export-api="exportBuildings" table-selector="#buildingsTable" :query-form="searchForm" file-name="实物资产档案-房屋建筑物.xls" excel-title="实物资产档案-房屋建筑物" :date-fields="exportDateFields" :show-dropdown="true" :all-data-page-size="10000" button-type="primary" :auto-exclude-operations="true" :exclude-columns="[]" :table-columns="tableColumns" @export-success="handleExportSuccess" @export-error="handleExportError" @export-all-success="handleExportAllSuccess" @export-all-error="handleExportAllError" />
          </el-form-item>
        </div>
      </el-form>
    </div>

    <!-- 数据表格 -->
    <div class="table-section">
      <el-table id="buildingsTable" :data="tableData" :height="470" border stripe highlight-current-row style="width: 100%;" v-loading="loading" row-key="zchiId">
        <el-table-column type="index" label="序号" width="60" align="center" />
        <el-table-column prop="zchiAssetsNo" label="资产编号" width="100" show-overflow-tooltip align="center" />
        <el-table-column prop="zchiAssetsName" label="资产名称" width="120" show-overflow-tooltip align="center" />
        <el-table-column prop="zchiUseDescribe" label="现状/用途" width="120" show-overflow-tooltip align="center" />
        <el-table-column prop="companyName" label="产权单位" width="120" show-overflow-tooltip align="center" />
        <el-table-column prop="zchiCertificateCode" label="产权证号" width="100" show-overflow-tooltip align="center" />
        <el-table-column prop="zchiAddress" label="地理位置" width="140" show-overflow-tooltip align="center" />
        <el-table-column prop="zchiHouseSource" label="取得方式" width="100" show-overflow-tooltip align="center" />
        <el-table-column prop="zchiDate" label="取得时间" width="120" show-overflow-tooltip align="center" />
        <el-table-column prop="zchiArea" label="建筑面积(㎡)" width="120" align="center" />
        <el-table-column prop="zchiOriginalValue" label="原值(万元)" width="120" show-overflow-tooltip align="center" />
        <el-table-column prop="zchiNetValue" label="净值(万元)" width="120" show-overflow-tooltip align="center" />
        <el-table-column prop="zchiTotalDepreciation" label="累计折旧(万元)" width="140" show-overflow-tooltip align="center" />
        <el-table-column prop="zchiCountry" label="境内/境外" width="100" show-overflow-tooltip align="center" />
        <el-table-column prop="zchiIfAssets" label="是否两非资产" width="120" align="center" />
        <el-table-column prop="zchiIfExist" label="是否取得房屋产权证" width="150" show-overflow-tooltip align="center" />
        <el-table-column prop="zchiIfDispute" label="是否存在纠纷" width="120" show-overflow-tooltip align="center" />
        <el-table-column prop="zchiIfDispose" label="是否可处置" width="120" show-overflow-tooltip align="center" />
        <el-table-column prop="zchiIfMortgage" label="是否存在抵押" width="120" show-overflow-tooltip align="center" />
        <el-table-column prop="zchiOperator" label="联系人" width="100" show-overflow-tooltip align="center" />
        <el-table-column prop="zchiOperatorTel" label="联系电话" width="120" show-overflow-tooltip align="center" />
        <el-table-column prop="zchiProvince" label="省份" width="100" show-overflow-tooltip align="center" />
        <el-table-column prop="zchiCity" label="城市" width="100" show-overflow-tooltip align="center" />
        <el-table-column prop="zchiCounty" label="区/县" width="100" show-overflow-tooltip align="center" />
        <el-table-column prop="zchiDeptName" label="业务管理部门" width="140" show-overflow-tooltip align="center" />
        <el-table-column prop="zchiDepartmentLeader" label="部门负责人" width="120" show-overflow-tooltip align="center" />
        <el-table-column prop="zchiDepartmentTel" label="部门电话" width="120" show-overflow-tooltip align="center" />
        <el-table-column prop="zchiCompanyLeader" label="分公司负责人" width="120" show-overflow-tooltip align="center" />
        <el-table-column prop="zchiCompanyTel" label="分公司电话" width="120" show-overflow-tooltip align="center" />
        <el-table-column prop="zchiEvaluateValue" label="评估价值(万元)" width="140" show-overflow-tooltip align="center" />
        <el-table-column prop="zchiEvaluateDate" label="评估日期" width="120" show-overflow-tooltip align="center" />
        <el-table-column prop="zchiServiceLife" label="使用年限(年)" width="120" show-overflow-tooltip align="center" />
        <el-table-column prop="zchiDepreciableYear" label="计提年限" width="100" show-overflow-tooltip align="center" />
        <el-table-column prop="zchiOfficeArea" label="科研办公面积" width="120" show-overflow-tooltip align="center" />
        <el-table-column prop="zchiCommercialArea" label="商业面积" width="100" show-overflow-tooltip align="center" />
        <el-table-column prop="zchiResidentialArea" label="住宅面积" width="100" show-overflow-tooltip align="center" />
        <el-table-column prop="zchiIndustrialArea" label="工业面积" width="100" show-overflow-tooltip align="center" />
        <el-table-column prop="zchiUndergroundArea" label="地下建筑面积" width="120" show-overflow-tooltip align="center" />
        <el-table-column prop="zchiOtherArea" label="其他面积" width="100" show-overflow-tooltip align="center" />
        <el-table-column prop="zchiRemark" label="备注" width="150" show-overflow-tooltip align="center" />
        <el-table-column prop="createdTime" label="创建时间" width="120" show-overflow-tooltip align="center" />
        <el-table-column prop="createdBy" label="创建人" width="100" show-overflow-tooltip align="center" />
        <el-table-column prop="updatedTime" label="更新时间" width="120" show-overflow-tooltip align="center" />
        <el-table-column label="操作" width="80" fixed="right" align="center">
          <template slot-scope="scope">
            <el-button type="text" size="small" @click="handleDetail(scope.row)">详情</el-button>
          </template>
        </el-table-column>
      </el-table>

      <!-- 分页 -->
      <el-pagination background class="el-pagination-a b-none" @current-change="handleCurrentChange" @size-change="handleSizeChange" :current-page="searchForm.pageNo" :page-size="searchForm.pageSize" :page-sizes="[10, 20, 50, 100]" layout="total, sizes, prev, pager, next, jumper" :total="total" />
    </div>

    <!-- 详情弹窗组件 -->
    <building-detail-dialog ref="buildingDetail" />

    <!-- 高级查询弹窗组件 -->
    <building-advanced-search-dialog ref="buildingAdvancedSearch" :external-search-form="searchForm" @search="handleAdvancedSearchSubmit" />
  </div>
</template>

<script>
import { getBuildingsList, exportBuildings, getBuildingsStats } from '@/api/buildings'
import BuildingDetailDialog from './components/BuildingDetailDialog.vue'
import BuildingAdvancedSearchDialog from './components/BuildingAdvancedSearchDialog.vue'
import ExportButton from '@/components/ExportButton/index.vue'

export default {
  name: "index",
  components: {
    BuildingDetailDialog,
    BuildingAdvancedSearchDialog,
    ExportButton
  },
  data () {
    return {
      searchForm: {
        zchiAssetsNo: '',
        zchiCertificateCode: '',
        companyName: '',
        zchiOperator: '',
        zchiCity: '',
        zchiBusinessDirection: '',
        zchiUseDescribe: '',
        zchiIfDispose: '',
        // 高级查询字段
        zchiAssetsName: '',
        zchiAddress: '',
        zchiHouseSource: '',
        zchiDate: '',
        zchiArea: '',
        zchiOriginalValue: '',
        zchiNetValue: '',
        zchiTotalDepreciation: '',
        zchiCountry: '',
        zchiIfAssets: '',
        zchiIfExist: '',
        zchiIfDispute: '',
        zchiIfMortgage: '',
        zchiOperatorTel: '',
        zchiProvince: '',
        zchiCounty: '',
        zchiDeptName: '',
        zchiDepartmentLeader: '',
        zchiDepartmentTel: '',
        zchiCompanyLeader: '',
        zchiCompanyTel: '',
        zchiEvaluateValue: '',
        zchiEvaluateDate: '',
        zchiServiceLife: '',
        zchiDepreciableYear: '',
        zchiOfficeArea: '',
        zchiCommercialArea: '',
        zchiResidentialArea: '',
        zchiIndustrialArea: '',
        zchiUndergroundArea: '',
        zchiOtherArea: '',
        zchiRemark: '',
        createdBy: '',
        pageNo: 1,
        pageSize: 10
      },
      tableData: [],
      total: 0,
      loading: false,
      statistics: {
        totalCount: 0,
        totalValue: 0,
        disposalCount: 0,
        leaseCount: 0
      },

      // 表格列配置，用于导出
      tableColumns: [
        { prop: 'zchiAssetsNo', label: '资产编号' },
        { prop: 'zchiAssetsName', label: '资产名称' },
        { prop: 'zchiUseDescribe', label: '现状/用途' },
        { prop: 'companyName', label: '产权单位' },
        { prop: 'zchiCertificateCode', label: '产权证号' },
        { prop: 'zchiAddress', label: '地理位置' },
        { prop: 'zchiHouseSource', label: '取得方式' },
        { prop: 'zchiDate', label: '取得时间' },
        { prop: 'zchiArea', label: '建筑面积(㎡)' },
        { prop: 'zchiOriginalValue', label: '原值(万元)' },
        { prop: 'zchiNetValue', label: '净值(万元)' },
        { prop: 'zchiTotalDepreciation', label: '累计折旧(万元)' },
        { prop: 'zchiCountry', label: '境内/境外' },
        { prop: 'zchiIfAssets', label: '是否两非资产' },
        { prop: 'zchiIfExist', label: '是否取得房屋产权证' },
        { prop: 'zchiIfDispute', label: '是否存在纠纷' },
        { prop: 'zchiIfDispose', label: '是否可处置' },
        { prop: 'zchiIfMortgage', label: '是否存在抵押' },
        { prop: 'zchiOperator', label: '联系人' },
        { prop: 'zchiOperatorTel', label: '联系电话' },
        { prop: 'zchiProvince', label: '省份' },
        { prop: 'zchiCity', label: '城市' },
        { prop: 'zchiCounty', label: '区/县' },
        { prop: 'zchiDeptName', label: '业务管理部门' },
        { prop: 'zchiDepartmentLeader', label: '部门负责人' },
        { prop: 'zchiDepartmentTel', label: '部门电话' },
        { prop: 'zchiCompanyLeader', label: '分公司负责人' },
        { prop: 'zchiCompanyTel', label: '分公司电话' },
        { prop: 'zchiEvaluateValue', label: '评估价值(万元)' },
        { prop: 'zchiEvaluateDate', label: '评估日期' },
        { prop: 'zchiServiceLife', label: '使用年限(年)' },
        { prop: 'zchiDepreciableYear', label: '计提年限' },
        { prop: 'zchiOfficeArea', label: '科研办公面积' },
        { prop: 'zchiCommercialArea', label: '商业面积' },
        { prop: 'zchiResidentialArea', label: '住宅面积' },
        { prop: 'zchiIndustrialArea', label: '工业面积' },
        { prop: 'zchiUndergroundArea', label: '地下建筑面积' },
        { prop: 'zchiOtherArea', label: '其他面积' },
        { prop: 'zchiRemark', label: '备注' },
        { prop: 'createdTime', label: '创建时间' },
        { prop: 'createdBy', label: '创建人' },
        { prop: 'updatedTime', label: '更新时间' }
      ],

      // 导出日期字段配置
      exportDateFields: {
        zchiDate: { celltype: "text" },
        zchiEvaluateDate: { celltype: "text" },
        createdTime: { celltype: "text" },
        updatedTime: { celltype: "text" }
      },

      // 导出API函数
      exportBuildings
    }
  },
  created () {
    this.fetchData()
    this.fetchStatistics()
  },
  methods: {
    fetchData () {
      this.loading = true
      const query = {
        ...this.searchForm
      }

      getBuildingsList(query).then(response => {
        if (response && response.data) {
          this.tableData = response.data.list || []
          this.total = response.data.total || 0
        } else {
          this.tableData = []
          this.total = 0
        }
        this.loading = false
      }).catch(() => {
        this.loading = false
        this.$message.error('获取数据失败')
      })
    },

    fetchStatistics () {
      getBuildingsStats().then(response => {
        if (response && response.data) {
          this.statistics = {
            totalCount: response.data.TOTAL_HOUSE_COUNT || 0,
            totalValue: response.data.TOTAL_NET_VALUE || 0,
            disposalCount: response.data.disposalCount || 0,
            leaseCount: response.data.leaseCount || 0
          }
        }
      }).catch(() => {
        this.$message.error('获取统计数据失败')
      })
    },

    onSearch () {
      this.searchForm.pageNo = 1
      this.fetchData()
    },

    resetQuery () {
      this.searchForm.pageNo = 1
      // 重置所有查询字段
      Object.keys(this.searchForm).forEach(key => {
        this.searchForm[key] = ''
      })
      this.fetchData()
    },

    // 导出成功回调
    handleExportSuccess (response) {
      console.log('当前数据导出成功:', response)
      // 可以在这里添加额外的成功处理逻辑
    },

    // 导出失败回调
    handleExportError (error) {
      console.error('当前数据导出失败:', error)
      // 可以在这里添加额外的错误处理逻辑
    },

    // 全部数据导出成功回调
    handleExportAllSuccess (response) {
      console.log('全部数据导出成功:', response)
      // 可以在这里添加额外的成功处理逻辑
    },

    // 全部数据导出失败回调
    handleExportAllError (error) {
      console.error('全部数据导出失败:', error)
      // 可以在这里添加额外的错误处理逻辑
    },

    handleDetail (row) {
      this.$refs.buildingDetail.showDialog(row)
    },

    handleAdvancedSearch () {
      this.$refs.buildingAdvancedSearch.showDialog()
    },

    handleAdvancedSearchSubmit (searchParams) {
      // 直接使用高级查询的完整参数替换当前搜索表单
      this.searchForm = { ...searchParams }
      this.searchForm.pageNo = 1
      this.fetchData()
    },

    handleSizeChange (val) {
      this.searchForm.pageSize = val
      this.fetchData()
    },

    handleCurrentChange (val) {
      this.searchForm.pageNo = 1
      this.fetchData()
    }
  }
}
</script>

<style lang="scss" scoped>
.leftRight {
  display: flex;
  justify-content: space-between;
}
.inputW {
  width: 250px;
}

.custom-table-container {
  padding: 16px;
  background-color: #f5f7fa;
}

/* 顶部统计卡片样式 */
.statistics-cards {
  display: flex;
  justify-content: space-between;
  margin-bottom: 20px;
}

.stat-card {
  flex: 1;
  display: flex;
  align-items: center;
  background-color: #fff;
  padding: 20px;
  border-radius: 4px;
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.05);
  margin-right: 15px;
}

.stat-card:last-child {
  margin-right: 0;
}

.icon-wrapper {
  width: 48px;
  height: 48px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 15px;
}

.icon-wrapper i {
  font-size: 24px;
  color: #fff;
}

.red-bg {
  background-color: rgba(245, 108, 108, 0.2);
}

.orange-bg {
  background-color: rgba(230, 162, 60, 0.2);
}

.blue-bg {
  background-color: rgba(64, 158, 255, 0.2);
}

.purple-bg {
  background-color: rgba(103, 194, 58, 0.2);
}

.stat-info {
  flex: 1;
}

.stat-title {
  font-size: 14px;
  color: #606266;
  margin-bottom: 8px;
}

.stat-value {
  font-size: 20px;
  font-weight: bold;
}

.stat-value span {
  font-size: 14px;
  margin-left: 4px;
}

.red-text {
  color: #f56c6c;
}

.orange-text {
  color: #e6a23c;
}

.blue-text {
  color: #409eff;
}

.purple-text {
  color: #67c23a;
}

/* 搜索表单样式 */
.search-form {
  background-color: #fff;
  padding: 20px 20px 0 20px;
  border-radius: 4px;
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.05);
  margin-bottom: 20px;
}

/* 数据表格样式 */
.table-section {
  background: white;
  border-radius: 4px;
}
</style>
