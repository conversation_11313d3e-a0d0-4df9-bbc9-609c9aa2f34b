<!--园区资产高级查询弹窗组件-->
<template>
  <base-dialog title="高级查询" type="edit" :visible.sync="dialogVisible" size="Max" @confirm="handleConfirm" @cancel="handleCancel">
    <el-form ref="searchForm" :model="searchForm" label-width="120px" class="advanced-search-form">
      <!-- 基本信息 -->
      <div class="form-section">
        <div class="section-title">基本信息</div>
        <el-row :gutter="20">
          <el-col :span="8">
            <el-form-item label="资产编号">
              <el-input v-model="searchForm.zpkAssetNumber" placeholder="请输入资产编号" clearable />
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="资产名称">
              <el-input v-model="searchForm.zpkAssetName" placeholder="请输入资产名称" clearable />
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="资产类型">
              <el-input v-model="searchForm.zpkAssetType" placeholder="请输入资产类型" clearable />
            </el-form-item>
          </el-col>
        </el-row>
        <el-row :gutter="20">
          <el-col :span="8">
            <el-form-item label="现状用途">
              <el-input v-model="searchForm.zpkCurrentUsageDescription" placeholder="请输入现状用途" clearable />
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="所在地区">
              <el-input v-model="searchForm.zpkLocationProvinceCityDistrict" placeholder="请输入所在地区" clearable />
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="具体位置">
              <el-input v-model="searchForm.zpkSpecificLocation" placeholder="请输入具体位置" clearable />
            </el-form-item>
          </el-col>
        </el-row>
        <el-row :gutter="20">
          <el-col :span="8">
            <el-form-item label="主要经营方向">
              <el-input v-model="searchForm.zpkMainBusinessDirection" placeholder="请输入主要经营方向" clearable />
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="境内/境外">
              <el-input v-model="searchForm.zpkDomesticOrForeign" placeholder="请输入境内/境外" clearable />
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="是否两非资产">
              <el-input v-model="searchForm.zpkIsNonCoreAsset" placeholder="请输入是否两非资产" clearable />
            </el-form-item>
          </el-col>
        </el-row>
        <el-row :gutter="20">
          <el-col :span="8">
            <el-form-item label="是否存在纠纷">
              <el-input v-model="searchForm.zpkHasDispute" placeholder="请输入是否存在纠纷" clearable />
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="是否存在抵押">
              <el-input v-model="searchForm.zpkHasMortgage" placeholder="请输入是否存在抵押" clearable />
            </el-form-item>
          </el-col>
        </el-row>
      </div>

      <!-- 数值信息 -->
      <div class="form-section">
        <div class="section-title">数值信息</div>
        <el-row :gutter="20">
          <el-col :span="8">
            <el-form-item label="占地面积(㎡)">
              <el-input v-model="searchForm.zpkTotalArea" placeholder="请输入占地面积" clearable />
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="原值(万元)">
              <el-input v-model="searchForm.zpkOriginalValue" placeholder="请输入原值" clearable />
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="净值(万元)">
              <el-input v-model="searchForm.zpkNetValue" placeholder="请输入净值" clearable />
            </el-form-item>
          </el-col>
        </el-row>
      </div>

      <!-- 面积信息 -->
      <div class="form-section">
        <div class="section-title">面积信息</div>
        <el-row :gutter="20">
          <el-col :span="8">
            <el-form-item label="科研办公面积">
              <el-input v-model="searchForm.zpkResearchOfficeArea" placeholder="请输入科研办公面积" clearable />
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="商业面积">
              <el-input v-model="searchForm.zpkCommercialArea" placeholder="请输入商业面积" clearable />
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="住宅面积">
              <el-input v-model="searchForm.zpkResidentialArea" placeholder="请输入住宅面积" clearable />
            </el-form-item>
          </el-col>
        </el-row>
        <el-row :gutter="20">
          <el-col :span="8">
            <el-form-item label="工业面积">
              <el-input v-model="searchForm.zpkIndustrialArea" placeholder="请输入工业面积" clearable />
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="出租面积">
              <el-input v-model="searchForm.zpkRentalArea" placeholder="请输入出租面积" clearable />
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="自用面积">
              <el-input v-model="searchForm.zpkSelfUseArea" placeholder="请输入自用面积" clearable />
            </el-form-item>
          </el-col>
        </el-row>
        <el-row :gutter="20">
          <el-col :span="8">
            <el-form-item label="闲置面积">
              <el-input v-model="searchForm.zpkIdleArea" placeholder="请输入闲置面积" clearable />
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="其他面积">
              <el-input v-model="searchForm.zpkOtherArea" placeholder="请输入其他面积" clearable />
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="已抵押面积">
              <el-input v-model="searchForm.zpkMortgagedArea" placeholder="请输入已抵押面积" clearable />
            </el-form-item>
          </el-col>
        </el-row>
      </div>

      <!-- 联系信息 -->
      <div class="form-section">
        <div class="section-title">联系信息</div>
        <el-row :gutter="20">
          <el-col :span="8">
            <el-form-item label="联系人">
              <el-input v-model="searchForm.zpkOperator" placeholder="请输入联系人" clearable />
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="联系电话">
              <el-input v-model="searchForm.zpkOperatorContact" placeholder="请输入联系电话" clearable />
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="部门负责人">
              <el-input v-model="searchForm.zpkDepartmentLeader" placeholder="请输入部门负责人" clearable />
            </el-form-item>
          </el-col>
        </el-row>
        <el-row :gutter="20">
          <el-col :span="8">
            <el-form-item label="部门电话">
              <el-input v-model="searchForm.zpkDepartmentLeaderContact" placeholder="请输入部门电话" clearable />
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="分公司负责人">
              <el-input v-model="searchForm.zpkCompanyLeader" placeholder="请输入分公司负责人" clearable />
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="分公司电话">
              <el-input v-model="searchForm.zpkCompanyLeaderContact" placeholder="请输入分公司电话" clearable />
            </el-form-item>
          </el-col>
        </el-row>
      </div>

      <!-- 其他信息 -->
      <div class="form-section">
        <div class="section-title">其他信息</div>
        <el-row :gutter="20">
          <el-col :span="8">
            <el-form-item label="备注">
              <el-input v-model="searchForm.zpkRemarks" placeholder="请输入备注" clearable />
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="创建人">
              <el-input v-model="searchForm.zpkCreatedBy" placeholder="请输入创建人" clearable />
            </el-form-item>
          </el-col>
        </el-row>
      </div>
    </el-form>

    <!-- 底部操作按钮 -->
    <template #footer>
      <div class="dialog-footer">
        <el-button @click="handleReset">重置</el-button>
        <el-button @click="handleCancel">取消</el-button>
        <el-button type="primary" @click="handleConfirm">查询</el-button>
      </div>
    </template>
  </base-dialog>
</template>

<script>
import BaseDialog from '@/components/BaseDialog/index.vue'

export default {
  name: 'ParkAdvancedSearchDialog',
  components: {
    BaseDialog
  },
  props: {
    // 外部传入的查询条件
    externalSearchForm: {
      type: Object,
      default: () => ({})
    }
  },
  data () {
    return {
      dialogVisible: false,
      searchForm: {
        zpkAssetNumber: '',
        zpkAssetName: '',
        zpkAssetType: '',
        zpkCurrentUsageDescription: '',
        zpkLocationProvinceCityDistrict: '',
        zpkSpecificLocation: '',
        zpkTotalArea: '',
        zpkOriginalValue: '',
        zpkNetValue: '',
        zpkMainBusinessDirection: '',
        zpkDomesticOrForeign: '',
        zpkIsNonCoreAsset: '',
        zpkHasDispute: '',
        zpkHasMortgage: '',
        zpkOperator: '',
        zpkOperatorContact: '',
        zpkResearchOfficeArea: '',
        zpkCommercialArea: '',
        zpkResidentialArea: '',
        zpkIndustrialArea: '',
        zpkRentalArea: '',
        zpkSelfUseArea: '',
        zpkIdleArea: '',
        zpkOtherArea: '',
        zpkMortgagedArea: '',
        zpkDepartmentLeader: '',
        zpkDepartmentLeaderContact: '',
        zpkCompanyLeader: '',
        zpkCompanyLeaderContact: '',
        zpkRemarks: '',
        zpkCreatedBy: ''
      }
    }
  },
  methods: {
    // 显示弹窗
    showDialog () {
      // 同步外部查询条件到弹窗表单
      this.syncExternalSearchForm()
      this.dialogVisible = true
    },

    // 同步外部查询条件
    syncExternalSearchForm () {
      // 将外部查询条件同步到弹窗表单中
      Object.keys(this.searchForm).forEach(key => {
        if (this.externalSearchForm[key] !== undefined) {
          this.searchForm[key] = this.externalSearchForm[key]
        }
      })
    },

    // 关闭弹窗
    closeDialog () {
      this.dialogVisible = false
    },

    // 确认查询
    handleConfirm () {
      // 创建完整的查询参数对象，包含所有字段（空值也保留，用于清空外部对应字段）
      const searchParams = { ...this.searchForm }

      // 清理字符串字段的前后空格
      Object.keys(searchParams).forEach(key => {
        if (typeof searchParams[key] === 'string') {
          searchParams[key] = searchParams[key].trim()
        }
      })

      this.$emit('search', searchParams)
      this.closeDialog()
    },

    // 取消
    handleCancel () {
      this.closeDialog()
    },

    // 重置表单
    handleReset () {
      this.$refs.searchForm.resetFields()
      // 手动重置所有字段
      Object.keys(this.searchForm).forEach(key => {
        this.searchForm[key] = ''
      })
    }
  }
}
</script>

<style lang="scss" scoped>
.advanced-search-form {
  max-height: 60vh;
  overflow-y: auto;
  padding-right: 10px;
}

.form-section {
  margin-bottom: 30px;

  .section-title {
    font-size: 16px;
    font-weight: bold;
    color: #303133;
    margin-bottom: 20px;
    padding-bottom: 10px;
    border-bottom: 2px solid #409eff;
    position: relative;

    &::before {
      content: '';
      position: absolute;
      left: 0;
      bottom: -2px;
      width: 30px;
      height: 2px;
      background-color: #409eff;
    }
  }
}

.dialog-footer {
  text-align: right;

  .el-button {
    margin-left: 10px;
  }
}

:deep(.el-form-item) {
  margin-bottom: 20px;
}

:deep(.el-form-item__label) {
  color: #606266;
  font-weight: 500;
}
</style>
