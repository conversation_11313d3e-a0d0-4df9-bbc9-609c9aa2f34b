<!--房屋建筑物高级查询弹窗组件-->
<template>
  <base-dialog title="高级查询" type="edit" :visible.sync="dialogVisible" size="Max" @confirm="handleConfirm" @cancel="handleCancel">
    <el-form ref="searchForm" :model="searchForm" label-width="120px" class="advanced-search-form">
      <!-- 基本信息 -->
      <div class="form-section">
        <div class="section-title">基本信息</div>
        <el-row :gutter="20">
          <el-col :span="8">
            <el-form-item label="资产编号">
              <el-input v-model="searchForm.zchiAssetsNo" placeholder="请输入资产编号" clearable />
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="资产名称">
              <el-input v-model="searchForm.zchiAssetsName" placeholder="请输入资产名称" clearable />
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="现状/用途">
              <el-input v-model="searchForm.zchiUseDescribe" placeholder="请输入现状/用途" clearable />
            </el-form-item>
          </el-col>
        </el-row>
        <el-row :gutter="20">
          <el-col :span="8">
            <el-form-item label="产权单位">
              <el-input v-model="searchForm.companyName" placeholder="请输入产权单位" clearable />
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="产权证号">
              <el-input v-model="searchForm.zchiCertificateCode" placeholder="请输入产权证号" clearable />
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="地理位置">
              <el-input v-model="searchForm.zchiAddress" placeholder="请输入地理位置" clearable />
            </el-form-item>
          </el-col>
        </el-row>
        <el-row :gutter="20">
          <el-col :span="8">
            <el-form-item label="取得方式">
              <el-input v-model="searchForm.zchiHouseSource" placeholder="请输入取得方式" clearable />
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="取得时间">
              <el-input v-model="searchForm.zchiDate" placeholder="请输入取得时间" clearable />
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="境内/境外">
              <el-input v-model="searchForm.zchiCountry" placeholder="请输入境内/境外" clearable />
            </el-form-item>
          </el-col>
        </el-row>
        <el-row :gutter="20">
          <el-col :span="8">
            <el-form-item label="是否两非资产">
              <el-input v-model="searchForm.zchiIfAssets" placeholder="请输入是否两非资产" clearable />
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="是否取得房屋产权证">
              <el-input v-model="searchForm.zchiIfExist" placeholder="请输入是否取得房屋产权证" clearable />
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="是否存在纠纷">
              <el-input v-model="searchForm.zchiIfDispute" placeholder="请输入是否存在纠纷" clearable />
            </el-form-item>
          </el-col>
        </el-row>
        <el-row :gutter="20">
          <el-col :span="8">
            <el-form-item label="是否可处置">
              <el-input v-model="searchForm.zchiIfDispose" placeholder="请输入是否可处置" clearable />
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="是否存在抵押">
              <el-input v-model="searchForm.zchiIfMortgage" placeholder="请输入是否存在抵押" clearable />
            </el-form-item>
          </el-col>
        </el-row>
      </div>

      <!-- 数值信息 -->
      <div class="form-section">
        <div class="section-title">数值信息</div>
        <el-row :gutter="20">
          <el-col :span="8">
            <el-form-item label="建筑面积(㎡)">
              <el-input v-model="searchForm.zchiArea" placeholder="请输入建筑面积" clearable />
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="原值(万元)">
              <el-input v-model="searchForm.zchiOriginalValue" placeholder="请输入原值" clearable />
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="净值(万元)">
              <el-input v-model="searchForm.zchiNetValue" placeholder="请输入净值" clearable />
            </el-form-item>
          </el-col>
        </el-row>
        <el-row :gutter="20">
          <el-col :span="8">
            <el-form-item label="累计折旧(万元)">
              <el-input v-model="searchForm.zchiTotalDepreciation" placeholder="请输入累计折旧" clearable />
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="评估价值(万元)">
              <el-input v-model="searchForm.zchiEvaluateValue" placeholder="请输入评估价值" clearable />
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="评估日期">
              <el-input v-model="searchForm.zchiEvaluateDate" placeholder="请输入评估日期" clearable />
            </el-form-item>
          </el-col>
        </el-row>
        <el-row :gutter="20">
          <el-col :span="8">
            <el-form-item label="使用年限(年)">
              <el-input v-model="searchForm.zchiServiceLife" placeholder="请输入使用年限" clearable />
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="计提年限">
              <el-input v-model="searchForm.zchiDepreciableYear" placeholder="请输入计提年限" clearable />
            </el-form-item>
          </el-col>
        </el-row>
      </div>

      <!-- 地区信息 -->
      <div class="form-section">
        <div class="section-title">地区信息</div>
        <el-row :gutter="20">
          <el-col :span="8">
            <el-form-item label="省份">
              <el-input v-model="searchForm.zchiProvince" placeholder="请输入省份" clearable />
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="城市">
              <el-input v-model="searchForm.zchiCity" placeholder="请输入城市" clearable />
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="区/县">
              <el-input v-model="searchForm.zchiCounty" placeholder="请输入区/县" clearable />
            </el-form-item>
          </el-col>
        </el-row>
      </div>

      <!-- 面积信息 -->
      <div class="form-section">
        <div class="section-title">面积信息</div>
        <el-row :gutter="20">
          <el-col :span="8">
            <el-form-item label="科研办公面积">
              <el-input v-model="searchForm.zchiOfficeArea" placeholder="请输入科研办公面积" clearable />
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="商业面积">
              <el-input v-model="searchForm.zchiCommercialArea" placeholder="请输入商业面积" clearable />
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="住宅面积">
              <el-input v-model="searchForm.zchiResidentialArea" placeholder="请输入住宅面积" clearable />
            </el-form-item>
          </el-col>
        </el-row>
        <el-row :gutter="20">
          <el-col :span="8">
            <el-form-item label="工业面积">
              <el-input v-model="searchForm.zchiIndustrialArea" placeholder="请输入工业面积" clearable />
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="地下建筑面积">
              <el-input v-model="searchForm.zchiUndergroundArea" placeholder="请输入地下建筑面积" clearable />
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="其他面积">
              <el-input v-model="searchForm.zchiOtherArea" placeholder="请输入其他面积" clearable />
            </el-form-item>
          </el-col>
        </el-row>
      </div>

      <!-- 联系信息 -->
      <div class="form-section">
        <div class="section-title">联系信息</div>
        <el-row :gutter="20">
          <el-col :span="8">
            <el-form-item label="联系人">
              <el-input v-model="searchForm.zchiOperator" placeholder="请输入联系人" clearable />
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="联系电话">
              <el-input v-model="searchForm.zchiOperatorTel" placeholder="请输入联系电话" clearable />
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="业务管理部门">
              <el-input v-model="searchForm.zchiDeptName" placeholder="请输入业务管理部门" clearable />
            </el-form-item>
          </el-col>
        </el-row>
        <el-row :gutter="20">
          <el-col :span="8">
            <el-form-item label="部门负责人">
              <el-input v-model="searchForm.zchiDepartmentLeader" placeholder="请输入部门负责人" clearable />
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="部门电话">
              <el-input v-model="searchForm.zchiDepartmentTel" placeholder="请输入部门电话" clearable />
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="分公司负责人">
              <el-input v-model="searchForm.zchiCompanyLeader" placeholder="请输入分公司负责人" clearable />
            </el-form-item>
          </el-col>
        </el-row>
        <el-row :gutter="20">
          <el-col :span="8">
            <el-form-item label="分公司电话">
              <el-input v-model="searchForm.zchiCompanyTel" placeholder="请输入分公司电话" clearable />
            </el-form-item>
          </el-col>
        </el-row>
      </div>

      <!-- 其他信息 -->
      <div class="form-section">
        <div class="section-title">其他信息</div>
        <el-row :gutter="20">
          <el-col :span="8">
            <el-form-item label="备注">
              <el-input v-model="searchForm.zchiRemark" placeholder="请输入备注" clearable />
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="创建人">
              <el-input v-model="searchForm.createdBy" placeholder="请输入创建人" clearable />
            </el-form-item>
          </el-col>
        </el-row>
      </div>

      <!-- 联系信息 -->
      <div class="form-section">
        <div class="section-title">联系信息</div>
        <el-row :gutter="20">
          <el-col :span="8">
            <el-form-item label="联系人">
              <el-input v-model="searchForm.zchiOperator" placeholder="请输入联系人" clearable />
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="联系电话">
              <el-input v-model="searchForm.zchiOperatorTel" placeholder="请输入联系电话" clearable />
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="业务管理部门">
              <el-input v-model="searchForm.zchiDeptName" placeholder="请输入业务管理部门" clearable />
            </el-form-item>
          </el-col>
        </el-row>
        <el-row :gutter="20">
          <el-col :span="8">
            <el-form-item label="部门负责人">
              <el-input v-model="searchForm.zchiDepartmentLeader" placeholder="请输入部门负责人" clearable />
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="部门电话">
              <el-input v-model="searchForm.zchiDepartmentTel" placeholder="请输入部门电话" clearable />
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="分公司负责人">
              <el-input v-model="searchForm.zchiCompanyLeader" placeholder="请输入分公司负责人" clearable />
            </el-form-item>
          </el-col>
        </el-row>
        <el-row :gutter="20">
          <el-col :span="8">
            <el-form-item label="分公司电话">
              <el-input v-model="searchForm.zchiCompanyTel" placeholder="请输入分公司电话" clearable />
            </el-form-item>
          </el-col>
        </el-row>
      </div>

      <!-- 其他信息 -->
      <div class="form-section">
        <div class="section-title">其他信息</div>
        <el-row :gutter="20">
          <el-col :span="8">
            <el-form-item label="备注">
              <el-input v-model="searchForm.zchiRemark" placeholder="请输入备注" clearable />
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="创建人">
              <el-input v-model="searchForm.createdBy" placeholder="请输入创建人" clearable />
            </el-form-item>
          </el-col>
        </el-row>
      </div>
    </el-form>

    <!-- 底部操作按钮 -->
    <template #footer>
      <div class="dialog-footer">
        <el-button @click="handleReset">重置</el-button>
        <el-button @click="handleCancel">取消</el-button>
        <el-button type="primary" @click="handleConfirm">查询</el-button>
      </div>
    </template>
  </base-dialog>
</template>

<script>
import BaseDialog from '@/components/BaseDialog/index.vue'

export default {
  name: 'BuildingAdvancedSearchDialog',
  components: {
    BaseDialog
  },
  props: {
    // 外部传入的查询条件
    externalSearchForm: {
      type: Object,
      default: () => ({})
    }
  },
  data () {
    return {
      dialogVisible: false,
      searchForm: {
        zchiAssetsNo: '',
        zchiAssetsName: '',
        zchiUseDescribe: '',
        companyName: '',
        zchiCertificateCode: '',
        zchiAddress: '',
        zchiHouseSource: '',
        zchiDate: '',
        zchiArea: '',
        zchiOriginalValue: '',
        zchiNetValue: '',
        zchiTotalDepreciation: '',
        zchiCountry: '',
        zchiIfAssets: '',
        zchiIfExist: '',
        zchiIfDispute: '',
        zchiIfDispose: '',
        zchiIfMortgage: '',
        zchiOperator: '',
        zchiOperatorTel: '',
        zchiProvince: '',
        zchiCity: '',
        zchiCounty: '',
        zchiDeptName: '',
        zchiDepartmentLeader: '',
        zchiDepartmentTel: '',
        zchiCompanyLeader: '',
        zchiCompanyTel: '',
        zchiEvaluateValue: '',
        zchiEvaluateDate: '',
        zchiServiceLife: '',
        zchiDepreciableYear: '',
        zchiOfficeArea: '',
        zchiCommercialArea: '',
        zchiResidentialArea: '',
        zchiIndustrialArea: '',
        zchiUndergroundArea: '',
        zchiOtherArea: '',
        zchiRemark: '',
        createdBy: ''
      }
    }
  },
  methods: {
    // 显示弹窗
    showDialog () {
      // 同步外部查询条件到弹窗表单
      this.syncExternalSearchForm()
      this.dialogVisible = true
    },

    // 同步外部查询条件
    syncExternalSearchForm () {
      // 将外部查询条件同步到弹窗表单中
      Object.keys(this.searchForm).forEach(key => {
        if (this.externalSearchForm[key] !== undefined) {
          this.searchForm[key] = this.externalSearchForm[key]
        }
      })
    },

    // 关闭弹窗
    closeDialog () {
      this.dialogVisible = false
    },

    // 确认查询
    handleConfirm () {
      // 创建完整的查询参数对象，包含所有字段（空值也保留，用于清空外部对应字段）
      const searchParams = { ...this.searchForm }

      // 清理字符串字段的前后空格
      Object.keys(searchParams).forEach(key => {
        if (typeof searchParams[key] === 'string') {
          searchParams[key] = searchParams[key].trim()
        }
      })

      this.$emit('search', searchParams)
      this.closeDialog()
    },

    // 取消
    handleCancel () {
      this.closeDialog()
    },

    // 重置表单
    handleReset () {
      this.$refs.searchForm.resetFields()
      // 手动重置所有字段
      Object.keys(this.searchForm).forEach(key => {
        this.searchForm[key] = ''
      })
    }
  }
}
</script>

<style lang="scss" scoped>
.advanced-search-form {
  max-height: 60vh;
  overflow-y: auto;
  padding-right: 10px;
}

.form-section {
  margin-bottom: 30px;

  .section-title {
    font-size: 16px;
    font-weight: bold;
    color: #303133;
    margin-bottom: 20px;
    padding-bottom: 10px;
    border-bottom: 2px solid #409eff;
    position: relative;

    &::before {
      content: '';
      position: absolute;
      left: 0;
      bottom: -2px;
      width: 30px;
      height: 2px;
      background-color: #409eff;
    }
  }
}

.dialog-footer {
  text-align: right;

  .el-button {
    margin-left: 10px;
  }
}

:deep(.el-form-item) {
  margin-bottom: 20px;
}

:deep(.el-form-item__label) {
  color: #606266;
  font-weight: 500;
}
</style>
</script>
